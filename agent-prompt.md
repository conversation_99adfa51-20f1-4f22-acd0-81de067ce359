1. 将代码中中使用el-input的地方替换为使用src/components/bfButton/main.ts;
2. 将代码中使用el-input-number的地方替换为使用src/components/bfInputNumber/main.ts, 导入src/components/bfInputNumber/main.ts时应该将名称命名为bfInputNumberV2来使用, 且添加class="!w-full";
3. 将代码中使用el-select的地方替换为使用src/components/bfSelect/main.ts, 并添加class="!w-full !h-[50px]";
4. 将代码中使用el-checkbox的地方替换为使用src/components/bfCheckbox/main.ts;
5. 将代码中使用el-Radio的地方替换为使用src/components/bfRadio/main.ts;
6. 将代码中使用el-button的地方替换为使用src/components/bfButton/main.ts, 如果el-button上使用了class需要删除, 将type名称替换为color-type;
7. 将代码中使用el-transfer的地方替换为使用src/components/bfTransfer/main.ts;
8. 如果有el-form, 将表单设置为`左对齐`;
9. 如果有el-form-item使用, 将label+":", label设置为高度50px, 设置行高为50px;
10. 如果有使用@/components/common/generateDmrId, 将其替换为使用src/components/common/DmridInput.vue;
11. 如果上述完成后, 请继续检查代码中的Components都导入了哪些组件, 将这些组件内部代码继续按照agent-prompt.md来修改;
11. 将`第1-第9`点当作一个`task`来处理, 将`第9点`中的每一次组件都生成一个`task`来处理.
